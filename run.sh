#!/bin/bash

# 排列三数据抓取启动脚本

echo "🚀 排列三数据抓取启动脚本"
echo "================================"
echo "使用方法:"
echo "  ./run.sh                    # 抓取所有页面 (2-69页)"
echo "  ./run.sh 2 5                # 抓取第2-5页"
echo "  ./run.sh 10 20              # 抓取第10-20页"
echo "================================"
echo ""

# 检查参数
if [ $# -eq 0 ]; then
    echo "正在启动完整数据抓取 (第2-69页)..."
    ARGS=""
elif [ $# -eq 2 ]; then
    echo "正在启动数据抓取 (第$1-$2页)..."
    ARGS="--start $1 --end $2"
else
    echo "❌ 参数错误！"
    echo "用法: ./run.sh [起始页码] [结束页码]"
    exit 1
fi

echo "预计需要几分钟时间，请耐心等待..."
echo ""

# 激活虚拟环境并运行脚本
source .venv/bin/activate && python3 main.py $ARGS

echo ""
echo "✅ 抓取完成！请查看生成的Excel文件"
