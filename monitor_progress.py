#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控抓取进度的脚本
"""

import time
import os

def monitor_progress():
    """监控抓取进度"""
    print("正在监控抓取进度...")
    print("目标：抓取第2-69页，共68页")
    print("=" * 50)
    
    while True:
        # 检查是否有Excel文件生成
        if os.path.exists('排列三开奖历史数据.xlsx'):
            print("\n✅ 抓取完成！Excel文件已生成")
            break
        
        time.sleep(10)  # 每10秒检查一次
        print(".", end="", flush=True)

if __name__ == "__main__":
    monitor_progress()
