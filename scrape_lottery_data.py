#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排列三开奖历史数据抓取脚本
抓取网站：https://www.51879.com/kaijiang/p3/index_2.html 到 index_69.html
目标：抓取class为tab2_zj的table数据并汇总到Excel
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
from urllib.parse import urljoin
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LotteryDataScraper:
    def __init__(self):
        self.base_url = "https://www.51879.com/kaijiang/p3/"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.all_data = []

    def get_page_data(self, page_num):
        """获取单个页面的数据"""
        if page_num == 1:
            url = urljoin(self.base_url, "")  # 第一页没有index_1
        else:
            url = urljoin(self.base_url, f"index_{page_num}.html")

        logger.info(f"正在抓取第{page_num}页: {url}")

        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            # 首先尝试查找class为tab2_zj的table
            table = soup.find('table', class_='tab2_zj')

            # 如果没找到，尝试查找所有table
            if not table:
                tables = soup.find_all('table')
                logger.info(f"第{page_num}页找到{len(tables)}个table标签")

                # 查找包含开奖数据的table（通过内容特征识别）
                for t in tables:
                    text_content = t.get_text()
                    if '期号' in text_content and '开奖日期' in text_content and '开奖号码' in text_content:
                        table = t
                        logger.info(f"第{page_num}页通过内容特征找到数据表格")
                        break

            if not table:
                logger.warning(f"第{page_num}页未找到数据表格")
                return []

            # 解析表格数据
            rows = table.find_all('tr')
            page_data = []

            for i, row in enumerate(rows):
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 7:  # 确保有足够的列
                    row_data = []
                    for cell in cells:
                        # 提取文本，去除多余空白
                        text = cell.get_text(strip=True)
                        # 处理开奖号码格式（去除下划线）
                        if '_' in text and len(text.replace('_', '')) == 3:
                            text = text.replace('_', '')
                        row_data.append(text)

                    # 如果是表头行，跳过
                    if i == 0 and any(keyword in row_data[0] for keyword in ['期号', '开奖日期']):
                        continue

                    # 过滤掉空行或无效行
                    if len(row_data) >= 7 and row_data[0] and row_data[0].isdigit():
                        page_data.append(row_data)

            logger.info(f"第{page_num}页成功抓取{len(page_data)}条数据")
            return page_data

        except requests.RequestException as e:
            logger.error(f"第{page_num}页请求失败: {e}")
            return []
        except Exception as e:
            logger.error(f"第{page_num}页解析失败: {e}")
            return []

    def scrape_all_pages(self, start_page=2, end_page=69):
        """抓取所有页面的数据"""
        logger.info(f"开始抓取第{start_page}页到第{end_page}页的数据")
        
        for page_num in range(start_page, end_page + 1):
            page_data = self.get_page_data(page_num)
            self.all_data.extend(page_data)
            
            # 随机延时，避免请求过于频繁
            delay = random.uniform(1, 3)
            time.sleep(delay)
        
        logger.info(f"总共抓取了{len(self.all_data)}条数据")

    def save_to_excel(self, filename='排列三开奖历史数据.xlsx'):
        """保存数据到Excel文件"""
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
        
        # 定义列名
        columns = ['期号', '开奖日期', '开奖号码', '销售总额', '直选注数', '直选金额', 
                  '组三注数', '组三金额', '组六注数', '组六金额', '详情']
        
        # 创建DataFrame
        df = pd.DataFrame(self.all_data, columns=columns[:len(self.all_data[0])])
        
        # 保存到Excel
        try:
            df.to_excel(filename, index=False, engine='openpyxl')
            logger.info(f"数据已保存到 {filename}")
            logger.info(f"共保存 {len(df)} 条记录")
            
            # 显示前几行数据作为预览
            print("\n数据预览:")
            print(df.head())
            
        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")

    def run(self):
        """运行完整的抓取流程"""
        logger.info("开始执行排列三数据抓取任务")
        
        # 抓取所有页面数据
        self.scrape_all_pages()
        
        # 保存到Excel
        self.save_to_excel()
        
        logger.info("任务完成")

if __name__ == "__main__":
    scraper = LotteryDataScraper()
    scraper.run()
