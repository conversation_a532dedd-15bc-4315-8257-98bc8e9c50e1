#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证脚本 - 检查生成的Excel文件
"""

import pandas as pd
import os

def verify_excel_data():
    """验证Excel数据的完整性和正确性"""
    filename = '排列三开奖历史数据.xlsx'
    
    if not os.path.exists(filename):
        print(f"❌ 文件 {filename} 不存在")
        return
    
    try:
        # 读取Excel文件
        df = pd.read_excel(filename)
        
        print("=" * 60)
        print("📊 排列三开奖历史数据验证报告")
        print("=" * 60)
        
        # 基本信息
        print(f"📁 文件名: {filename}")
        print(f"📈 总记录数: {len(df)} 条")
        print(f"📋 列数: {len(df.columns)} 列")
        print(f"💾 文件大小: {os.path.getsize(filename) / 1024:.1f} KB")
        
        print("\n📊 列信息:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i}. {col}")
        
        # 数据质量检查
        print("\n🔍 数据质量检查:")
        
        # 检查空值
        null_counts = df.isnull().sum()
        if null_counts.sum() == 0:
            print("  ✅ 无空值")
        else:
            print("  ⚠️ 发现空值:")
            for col, count in null_counts[null_counts > 0].items():
                print(f"    - {col}: {count} 个空值")
        
        # 检查期号格式
        if '期号' in df.columns:
            period_col = df['期号'].astype(str)
            invalid_periods = period_col[~period_col.str.isdigit()]
            if len(invalid_periods) == 0:
                print("  ✅ 期号格式正确")
            else:
                print(f"  ⚠️ 发现 {len(invalid_periods)} 个无效期号")
        
        # 检查开奖号码格式
        if '开奖号码' in df.columns:
            lottery_col = df['开奖号码'].astype(str)
            valid_numbers = lottery_col[lottery_col.str.len() == 3]
            if len(valid_numbers) == len(df):
                print("  ✅ 开奖号码格式正确")
            else:
                print(f"  ⚠️ 发现 {len(df) - len(valid_numbers)} 个格式异常的开奖号码")
        
        # 数据范围
        print("\n📅 数据范围:")
        if '期号' in df.columns:
            periods = df['期号'].astype(str)
            print(f"  最早期号: {periods.iloc[-1]}")
            print(f"  最新期号: {periods.iloc[0]}")
        
        if '开奖日期' in df.columns:
            print(f"  日期范围: {df['开奖日期'].iloc[-1]} 到 {df['开奖日期'].iloc[0]}")
        
        # 显示前5条和后5条数据
        print("\n📋 数据预览 (前5条):")
        print(df.head().to_string(index=False))
        
        print("\n📋 数据预览 (后5条):")
        print(df.tail().to_string(index=False))
        
        print("\n" + "=" * 60)
        print("✅ 数据验证完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")

if __name__ == "__main__":
    verify_excel_data()
