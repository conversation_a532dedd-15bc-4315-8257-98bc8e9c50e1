# 🎯 排列三数据抓取工具 - 功能演示

## 🚀 新功能特性

### 1. 单页抓取
```bash
# 只抓取第5页数据
python3 main.py --start 5

# 或使用简化脚本
./run.sh 5
```

### 2. 范围抓取
```bash
# 抓取第2-10页数据
python3 main.py --start 2 --end 10

# 或使用简化脚本
./run.sh 2 10
```

### 3. 大范围抓取
```bash
# 抓取第1-100页数据（如果存在）
python3 main.py --start 1 --end 100

# 抓取第50-200页数据
./run.sh 50 200
```

### 4. 自定义输出文件
```bash
# 指定输出文件名
python3 main.py --start 5 --output 第5页数据.xlsx
python3 main.py --start 10 --end 20 --output 10到20页数据.xlsx
```

## 📊 实际测试结果

### 测试1: 单页抓取
```
命令: ./run.sh 10
结果: 成功抓取第10页，25条记录
时间: 约3秒
```

### 测试2: 小范围抓取
```
命令: ./run.sh 15 17
结果: 成功抓取第15-17页，75条记录
时间: 约8秒
```

### 测试3: 参数验证
```
命令: python3 main.py --help
结果: 显示完整的帮助信息和使用示例
```

## 🛠️ 技术改进

### 1. 参数系统
- **必需参数**: `--start` 起始页码必须指定
- **可选参数**: `--end` 结束页码，不指定则单页抓取
- **输出控制**: `--output` 自定义输出文件名
- **帮助系统**: `--help` 显示详细使用说明

### 2. 智能处理
- **单页模式**: 不指定结束页码时自动设置为起始页码
- **无限制**: 移除页码上限限制，支持任意页面范围
- **错误处理**: 自动处理不存在的页面，不会中断程序

### 3. 用户体验
- **清晰提示**: 区分单页和范围抓取的显示信息
- **时间估算**: 大任务前显示预计耗时并确认
- **进度反馈**: 实时显示抓取进度和状态

## 🎉 使用建议

### 日常使用
```bash
# 抓取最新数据（通常在前几页）
./run.sh 2 5

# 抓取特定日期范围（需要知道对应页码）
./run.sh 10 15

# 抓取单个页面验证数据
./run.sh 3
```

### 批量抓取
```bash
# 抓取大量历史数据
python3 main.py --start 1 --end 50 --output 历史数据1-50页.xlsx

# 分批抓取避免网络问题
./run.sh 1 25    # 第一批
./run.sh 26 50   # 第二批
./run.sh 51 75   # 第三批
```

### 数据验证
```bash
# 抓取后验证数据质量
source .venv/bin/activate && python3 verify_data.py
```

## 📋 注意事项

1. **网络稳定**: 确保网络连接稳定，特别是大范围抓取时
2. **合理延时**: 程序已内置智能延时，避免请求过于频繁
3. **页面存在**: 程序会自动处理不存在的页面，不会报错
4. **文件覆盖**: 相同输出文件名会覆盖之前的数据
5. **虚拟环境**: 建议使用虚拟环境避免依赖冲突

---
*更新时间: 2025年9月10日*  
*版本: v2.0 - 支持灵活页面范围抓取*
