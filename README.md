# 排列三开奖历史数据抓取工具

## 功能描述
这个工具用于抓取奇彩网(51879.com)上排列三开奖历史数据，从index_2.html到index_69.html共68个页面的数据，并将结果汇总到Excel表格中。

## 文件说明
- `scrape_lottery_data.py`: 主要的数据抓取脚本
- `requirements.txt`: Python依赖包列表
- `README.md`: 使用说明文档

## 安装依赖
```bash
pip install -r requirements.txt
```

## 使用方法
```bash
python scrape_lottery_data.py
```

## 输出文件
运行完成后会生成 `排列三开奖历史数据.xlsx` 文件，包含以下列：
- 期号
- 开奖日期
- 开奖号码
- 销售总额
- 直选注数
- 直选金额
- 组三注数
- 组三金额
- 组六注数
- 组六金额
- 详情

## 特性
- 自动处理68个页面的数据抓取
- 智能延时避免请求过于频繁
- 错误处理和日志记录
- 数据清洗和格式化
- 直接输出Excel格式

## 注意事项
- 请确保网络连接正常
- 抓取过程可能需要几分钟时间
- 如遇到网络问题，脚本会自动跳过并继续
- 建议在非高峰时段运行以获得更好的成功率
