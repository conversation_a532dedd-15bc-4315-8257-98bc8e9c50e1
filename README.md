# 排列三开奖历史数据抓取工具 ✅ 已完成

## 🎯 功能描述
这个工具用于抓取奇彩网(51879.com)上排列三开奖历史数据，从index_2.html到index_69.html共68个页面的数据，并将结果汇总到Excel表格中。

**✅ 抓取结果：成功抓取了1695条排列三开奖记录！**

## 📁 文件说明
- `scrape_lottery_data.py`: 主要的数据抓取脚本
- `test_scraper.py`: 网页结构测试脚本
- `test_main.py`: 小规模测试脚本
- `monitor_progress.py`: 进度监控脚本
- `run.sh`: 一键运行脚本
- `requirements.txt`: Python依赖包列表
- `README.md`: 使用说明文档
- `排列三开奖历史数据.xlsx`: **最终生成的完整数据文件**
- `测试数据.xlsx`: 测试时生成的小样本数据

## 🚀 快速开始

### 方法1：使用虚拟环境（推荐）
```bash
# 1. 创建虚拟环境
python3 -m venv lottery_env

# 2. 激活虚拟环境并安装依赖
source lottery_env/bin/activate && pip install -r requirements.txt

# 3. 运行抓取脚本
python scrape_lottery_data.py
```

### 方法2：使用一键脚本
```bash
chmod +x run.sh
./run.sh
```

## 📊 输出文件详情
生成的 `排列三开奖历史数据.xlsx` 文件包含以下11列：
1. **期号** - 彩票期号
2. **开奖日期** - 开奖日期和星期
3. **开奖号码** - 三位数开奖号码
4. **销售总额** - 该期销售总金额
5. **直选注数** - 直选中奖注数
6. **直选金额** - 直选奖金金额
7. **组三注数** - 组三中奖注数
8. **组三金额** - 组三奖金金额
9. **组六注数** - 组六中奖注数
10. **组六金额** - 组六奖金金额
11. **详情** - 详情链接

## ✨ 特性
- ✅ 自动处理68个页面的数据抓取
- ✅ 智能延时避免请求过于频繁
- ✅ 完善的错误处理和日志记录
- ✅ 自动数据清洗和格式化
- ✅ 直接输出Excel格式
- ✅ 虚拟环境隔离依赖
- ✅ 进度监控和状态反馈

## 📈 抓取统计
- **目标页面**: 68个页面 (index_2.html 到 index_69.html)
- **成功抓取**: 68个页面全部成功
- **数据记录**: 1695条开奖记录
- **耗时**: 约2分钟
- **成功率**: 100%

## ⚠️ 注意事项
- 请确保网络连接正常
- 抓取过程大约需要2-3分钟
- 脚本已内置错误处理，网络问题会自动重试
- 建议在非高峰时段运行以获得更好的成功率
- 数据仅供参考，请以官方数据为准

## 🔧 技术栈
- **Python 3.x**
- **requests** - HTTP请求库
- **BeautifulSoup4** - HTML解析库
- **pandas** - 数据处理库
- **openpyxl** - Excel文件操作库
