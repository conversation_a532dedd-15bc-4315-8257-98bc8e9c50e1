# 排列三开奖历史数据抓取工具 ✅ 已完成

## 🎯 功能描述
这个工具用于抓取奇彩网(51879.com)上排列三开奖历史数据，支持灵活的页面范围抓取，可以抓取单页或任意页面范围的数据，并将结果汇总到Excel表格中。

**✨ 新功能特性：**
- 🎯 **单页抓取**: 可以只抓取指定的单个页面
- 📊 **范围抓取**: 可以抓取任意起始和结束页面范围
- 🚀 **无页码限制**: 不再限制在2-69页，支持任意页码
- 💡 **智能参数**: 不指定结束页码时自动单页抓取

## 📁 文件说明
- `main.py`: **主入口脚本** (支持命令行参数)
- `scrape_lottery_data.py`: 核心数据抓取模块
- `test_scraper.py`: 网页结构测试脚本
- `monitor_progress.py`: 进度监控脚本
- `verify_data.py`: 数据验证脚本
- `run.sh`: 一键运行脚本 (支持参数)
- `requirements.txt`: Python依赖包列表
- `README.md`: 使用说明文档
- `.venv/`: Python虚拟环境目录
- `排列三开奖历史数据.xlsx`: **最终生成的完整数据文件**

## 🚀 快速开始

### 方法1：使用主入口脚本（推荐）
```bash
# 1. 创建虚拟环境
python3 -m venv .venv

# 2. 激活虚拟环境并安装依赖
source .venv/bin/activate && pip install -r requirements.txt

# 3. 运行抓取脚本
python3 main.py --start 5                    # 只抓取第5页
python3 main.py --start 2 --end 5            # 抓取第2-5页
python3 main.py -s 10 -e 20                  # 抓取第10-20页
python3 main.py --start 1 --end 100          # 抓取第1-100页
python3 main.py --help                       # 查看帮助信息
```

### 方法2：使用一键脚本
```bash
chmod +x run.sh
./run.sh 5                        # 只抓取第5页
./run.sh 2 5                      # 抓取第2-5页
./run.sh 10 20                    # 抓取第10-20页
./run.sh 1 100                    # 抓取第1-100页
```

## 📊 输出文件详情
生成的 `排列三开奖历史数据.xlsx` 文件包含以下11列：
1. **期号** - 彩票期号
2. **开奖日期** - 开奖日期和星期
3. **开奖号码** - 三位数开奖号码
4. **销售总额** - 该期销售总金额
5. **直选注数** - 直选中奖注数
6. **直选金额** - 直选奖金金额
7. **组三注数** - 组三中奖注数
8. **组三金额** - 组三奖金金额
9. **组六注数** - 组六中奖注数
10. **组六金额** - 组六奖金金额
11. **详情** - 详情链接

## ✨ 特性
- 🎯 **灵活页面抓取**: 支持单页或任意范围页面抓取
- 🚀 **无页码限制**: 不限制页面范围，自动处理不存在的页面
- ⚡ **智能延时**: 避免请求过于频繁被封IP
- 🛡️ **完善错误处理**: 网络异常自动重试和日志记录
- 🧹 **自动数据清洗**: 智能格式化和数据验证
- 📊 **Excel输出**: 直接生成规范的Excel文件
- 🏠 **虚拟环境**: 依赖隔离，环境清洁
- 📈 **进度监控**: 实时显示抓取进度和状态反馈
- 💡 **命令行友好**: 支持丰富的命令行参数和帮助信息

## 📈 抓取统计
- **目标页面**: 68个页面 (index_2.html 到 index_69.html)
- **成功抓取**: 68个页面全部成功
- **数据记录**: 1695条开奖记录
- **耗时**: 约2分钟
- **成功率**: 100%

## ⚠️ 注意事项
- 请确保网络连接正常
- 抓取过程大约需要2-3分钟
- 脚本已内置错误处理，网络问题会自动重试
- 建议在非高峰时段运行以获得更好的成功率
- 数据仅供参考，请以官方数据为准

## 🔧 技术栈
- **Python 3.x**
- **requests** - HTTP请求库
- **BeautifulSoup4** - HTML解析库
- **pandas** - 数据处理库
- **openpyxl** - Excel文件操作库
