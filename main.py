#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排列三开奖历史数据抓取主程序
支持命令行参数指定起始和结束页码
"""

import argparse
import sys
from scrape_lottery_data import LotteryDataScraper

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='排列三开奖历史数据抓取工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py --start 5                    # 只抓取第5页
  python main.py --start 2 --end 5            # 抓取第2-5页
  python main.py -s 10 -e 20                  # 抓取第10-20页
  python main.py --start 1 --end 100          # 抓取第1-100页
  python main.py -s 69                        # 只抓取第69页
        """
    )
    
    parser.add_argument(
        '-s', '--start',
        type=int,
        required=True,
        help='起始页码 (必需参数)'
    )

    parser.add_argument(
        '-e', '--end',
        type=int,
        help='结束页码 (可选，不指定则只抓取起始页)'
    )
    
    parser.add_argument(
        '-o', '--output',
        type=str,
        default='排列三开奖历史数据.xlsx',
        help='输出文件名 (默认: 排列三开奖历史数据.xlsx)'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='排列三数据抓取工具 v1.0'
    )
    
    return parser.parse_args()

def validate_arguments(args):
    """验证命令行参数"""
    if args.start < 1:
        print("❌ 错误: 起始页码不能小于1")
        return False

    # 如果没有指定结束页码，则设置为起始页码（单页抓取）
    if args.end is None:
        args.end = args.start

    if args.end < args.start:
        print("❌ 错误: 结束页码不能小于起始页码")
        return False

    # 移除页码上限限制，允许抓取任意页面
    # 网站可能有更多页面，让程序自动处理不存在的页面

    return True

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 验证参数
    if not validate_arguments(args):
        sys.exit(1)
    
    # 显示任务信息
    total_pages = args.end - args.start + 1
    print("🚀 排列三开奖历史数据抓取工具")
    print("=" * 50)
    if args.start == args.end:
        print(f"📄 抓取页面: 第{args.start}页")
    else:
        print(f"📄 抓取范围: 第{args.start}页 到 第{args.end}页")
    print(f"📊 总页数: {total_pages}页")
    print(f"💾 输出文件: {args.output}")
    print(f"🌐 目标网站: https://www.51879.com/kaijiang/p3/")
    print("=" * 50)
    
    # 确认执行
    if total_pages > 10:
        estimated_time = total_pages * 2
        if estimated_time > 60:
            time_str = f"{estimated_time // 60}分{estimated_time % 60}秒"
        else:
            time_str = f"{estimated_time}秒"
        confirm = input(f"即将抓取{total_pages}页数据，预计需要{time_str}，是否继续？(y/N): ")
        if confirm.lower() not in ['y', 'yes', '是']:
            print("❌ 用户取消操作")
            sys.exit(0)
    
    try:
        # 创建抓取器实例
        scraper = LotteryDataScraper()
        
        # 执行抓取
        scraper.scrape_all_pages(start_page=args.start, end_page=args.end)
        
        # 保存数据
        scraper.save_to_excel(args.output)
        
        print("\n🎉 任务完成!")
        print(f"✅ 数据已保存到: {args.output}")
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行过程中出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
