#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证网页结构和数据抓取逻辑
"""

import requests
from bs4 import BeautifulSoup

def test_page_structure():
    """测试单个页面的结构"""
    url = "https://www.51879.com/kaijiang/p3/index_2.html"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找所有table标签
        tables = soup.find_all('table')
        print(f"找到 {len(tables)} 个table标签")
        
        for i, table in enumerate(tables):
            class_attr = table.get('class', [])
            print(f"Table {i+1}: class = {class_attr}")
            
            # 查找class为tab2_zj的table
            if 'tab2_zj' in class_attr:
                print("找到目标table!")
                rows = table.find_all('tr')
                print(f"表格有 {len(rows)} 行")
                
                # 显示前几行数据
                for j, row in enumerate(rows[:5]):
                    cells = row.find_all(['td', 'th'])
                    row_data = [cell.get_text(strip=True) for cell in cells]
                    print(f"第{j+1}行: {row_data}")
                
                break
        else:
            print("未找到class为tab2_zj的table")
            # 显示页面中的所有class
            all_classes = set()
            for element in soup.find_all(class_=True):
                if isinstance(element.get('class'), list):
                    all_classes.update(element.get('class'))
                else:
                    all_classes.add(element.get('class'))
            print(f"页面中的所有class: {sorted(all_classes)}")
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_page_structure()
